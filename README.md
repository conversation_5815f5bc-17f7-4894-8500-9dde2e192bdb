## AI Document Translator (FastAPI)

REST API to translate full documents (DOCX, PDF, PPTX, TXT) using Hugging Face models like `facebook/nllb-200` or `facebook/mbart-large-50`, preserving formatting and layout as much as practical.

### Quickstart

1. Create and activate a virtual environment (recommended) and install deps:

```bash
pip install -r requirements.txt
```

2. Run the server:

```bash
uvicorn app.main:app --host 0.0.0.0 --port 8000
```

3. Translate a document via REST:

```bash
curl -X POST "http://localhost:8000/translate-doc" \
  -F "file=@/path/to/input.docx" \
  -F "target_language=German" \
  -F "model_name=facebook/nllb-200-distilled-600M" \
  -o translated.docx
```

OpenAPI docs at `http://localhost:8000/docs`.

### Notes

- Language detection uses `langdetect` heuristics on extracted text.
- NLLB expects language codes like `eng_Latn`, `deu_Latn`. We map common names.
- PDF translation recreates text layout by blocks using PyMuPDF. Complex PDFs with images/curves may not fully retain advanced formatting; consider pre-OCR for scanned PDFs.
- For mBART, supply `model_name=facebook/mbart-large-50-many-to-many-mmt` and target like `de_DE`.

## Streamlit UI

Start the API first (see above), then run Streamlit in a second terminal:

```bash
streamlit run streamlit_app.py
```

Use the sidebar to pick model and target language, upload a file, then click Translate and download the result.


