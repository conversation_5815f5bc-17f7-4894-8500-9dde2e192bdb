from fastapi import BackgroundTasks
from fastapi import <PERSON><PERSON><PERSON>, UploadFile, File, Form, HTTPException
from fastapi.responses import FileResponse
from starlette.middleware.cors import CORSMiddleware
import os
import tempfile
import shutil

from .services.translator import TranslationService
from .processors.docx_processor import translate_docx_file
from .processors.pptx_processor import translate_pptx_file
from .processors.pdf_processor import translate_pdf_file
from .processors.txt_processor import translate_txt_file
from .utils.lang import normalize_target, detect_language_code_for_model

app = FastAPI(title="AI Document Translator", version="1.0.0")

translation_jobs = {}
import uuid
import time

@app.post("/translate-doc-async")
async def translate_doc_async(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    target_language: str = Form(...),
    model_name: str = Form("facebook/m2m100_418M")
):
    if not file.filename:
        raise HTTPException(status_code=400, detail="Missing filename")
    filename = file.filename
    ext = os.path.splitext(filename)[1].lower()
    if ext not in {".docx", ".pdf", ".pptx", ".txt"}:
        raise HTTPException(status_code=400, detail="Unsupported file type")
    tmp_dir = tempfile.mkdtemp(prefix="docxtr_")
    input_path = os.path.join(tmp_dir, f"input{ext}")
    output_path = os.path.join(tmp_dir, f"output{ext}")
    with open(input_path, "wb") as f:
        shutil.copyfileobj(file.file, f)
    job_id = str(uuid.uuid4())
    def run_translation():
        translator_service.load_model_if_needed(model_name)
        tgt_code = normalize_target(target_language, model_name)
        src_iso = detect_language_code_for_model(input_path, ext, model_name)
        if src_iso is None:
            if "m2m100" in model_name.lower():
                src_iso = "en"
            elif "nllb" in model_name:
                src_iso = "eng_Latn"
            else:
                src_iso = "en_XX"
        translator_service.set_language_pair(src_iso=src_iso, tgt_code=tgt_code)
        if ext == ".docx":
            translate_docx_file(input_path, output_path, translator_service)
        elif ext == ".pptx":
            translate_pptx_file(input_path, output_path, translator_service)
        elif ext == ".pdf":
            translate_pdf_file(input_path, output_path, translator_service)
        elif ext == ".txt":
            translate_txt_file(input_path, output_path, translator_service)
        translation_jobs[job_id] = output_path
    background_tasks.add_task(run_translation)
    return {"job_id": job_id}

@app.get("/translate-doc-async/status/{job_id}")
async def get_translation_status(job_id: str):
    if job_id in translation_jobs:
        return {"status": "completed", "download_url": f"/translate-doc-async/download/{job_id}"}
    else:
        return {"status": "pending"}

@app.get("/translate-doc-async/download/{job_id}")
async def download_translated_file(job_id: str):
    if job_id not in translation_jobs:
        raise HTTPException(status_code=404, detail="Job not found")
    output_path = translation_jobs[job_id]
    ext = os.path.splitext(output_path)[1].lower()
    return FileResponse(
        output_path,
        media_type=_media_type_from_ext(ext),
        filename=os.path.basename(output_path),
    )
from fastapi import FastAPI, UploadFile, File, Form, HTTPException
from fastapi.responses import FileResponse
from starlette.middleware.cors import CORSMiddleware
import os
import tempfile
import shutil

from .services.translator import TranslationService
from .processors.docx_processor import translate_docx_file
from .processors.pptx_processor import translate_pptx_file
from .processors.pdf_processor import translate_pdf_file
from .processors.txt_processor import translate_txt_file
from .utils.lang import normalize_target, detect_language_code_for_model

app = FastAPI(title="AI Document Translator", version="1.0.0")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

translator_service = TranslationService()

@app.get("/health")
def health():
    return {"status": "ok"}

@app.post("/translate-doc")
async def translate_doc(
    file: UploadFile = File(...),
    target_language: str = Form(...),
    model_name: str = Form("facebook/m2m100_418M")  # Default to M2M100 model
):
    if not file.filename:
        raise HTTPException(status_code=400, detail="Missing filename")

    filename = file.filename
    ext = os.path.splitext(filename)[1].lower()

    if ext not in {".docx", ".pdf", ".pptx", ".txt"}:
        raise HTTPException(status_code=400, detail="Unsupported file type")

    # Prepare temp workspace
    tmp_dir = tempfile.mkdtemp(prefix="docxtr_")
    input_path = os.path.join(tmp_dir, f"input{ext}")
    output_path = os.path.join(tmp_dir, f"output{ext}")

    try:
        with open(input_path, "wb") as f:
            shutil.copyfileobj(file.file, f)

        # Configure translator
        translator_service.load_model_if_needed(model_name)
        tgt_code = normalize_target(target_language, model_name)
        src_iso = detect_language_code_for_model(input_path, ext, model_name)

        # Model-specific fallback language codes
        if src_iso is None:
            if "m2m100" in model_name.lower():
                src_iso = "en"  # M2M100 format
            elif "nllb" in model_name:
                src_iso = "eng_Latn"  # NLLB format
            else:
                src_iso = "en_XX"  # mBART format

        translator_service.set_language_pair(src_iso=src_iso, tgt_code=tgt_code)

        # Route by file type
        if ext == ".docx":
            translate_docx_file(input_path, output_path, translator_service)
        elif ext == ".pptx":
            translate_pptx_file(input_path, output_path, translator_service)
        elif ext == ".pdf":
            translate_pdf_file(input_path, output_path, translator_service)
        elif ext == ".txt":
            translate_txt_file(input_path, output_path, translator_service)

        download_name = _build_download_name(filename, target_language)

        return FileResponse(
            output_path,
            media_type=_media_type_from_ext(ext),
            filename=download_name,
        )

    finally:
        # cleanup handled automatically after response
        pass

@app.post("/translate-doc-advanced")
async def translate_doc_advanced(
    file: UploadFile = File(...),
    target_language: str = Form(...),
    model_name: str = Form("facebook/m2m100_418M"),
    source_language: str = Form(None)  # Optional manual source language override
):
    """Advanced translation endpoint with manual source language specification"""
    if not file.filename:
        raise HTTPException(status_code=400, detail="Missing filename")

    filename = file.filename
    ext = os.path.splitext(filename)[1].lower()

    if ext not in {".docx", ".pdf", ".pptx", ".txt"}:
        raise HTTPException(status_code=400, detail="Unsupported file type")

    # Prepare temp workspace
    tmp_dir = tempfile.mkdtemp(prefix="docxtr_")
    input_path = os.path.join(tmp_dir, f"input{ext}")
    output_path = os.path.join(tmp_dir, f"output{ext}")

    try:
        with open(input_path, "wb") as f:
            shutil.copyfileobj(file.file, f)

        # Configure translator
        translator_service.load_model_if_needed(model_name)
        tgt_code = normalize_target(target_language, model_name)
        
        # Use manual source language if provided, otherwise detect
        if source_language:
            src_iso = normalize_target(source_language, model_name)
        else:
            src_iso = detect_language_code_for_model(input_path, ext, model_name)

        # Model-specific fallback language codes
        if src_iso is None:
            if "m2m100" in model_name.lower():
                src_iso = "en"
            elif "nllb" in model_name:
                src_iso = "eng_Latn"
            else:
                src_iso = "en_XX"

        translator_service.set_language_pair(src_iso=src_iso, tgt_code=tgt_code)

        # Route by file type
        if ext == ".docx":
            translate_docx_file(input_path, output_path, translator_service)
        elif ext == ".pptx":
            translate_pptx_file(input_path, output_path, translator_service)
        elif ext == ".pdf":
            translate_pdf_file(input_path, output_path, translator_service)
        elif ext == ".txt":
            translate_txt_file(input_path, output_path, translator_service)

        download_name = _build_download_name(filename, target_language)

        return FileResponse(
            output_path,
            media_type=_media_type_from_ext(ext),
            filename=download_name,
        )

    finally:
        pass

@app.get("/supported-languages/{model_name}")
def get_supported_languages(model_name: str):
    """Get list of supported languages for a specific model"""
    if "m2m100" in model_name.lower():
        return {
            "model": model_name,
            "supported_languages": [
                "english", "german", "french", "spanish", "italian", "portuguese", 
                "russian", "arabic", "chinese", "mandarin", "japanese", "korean", 
                "dutch", "polish", "turkish", "hindi", "bengali", "urdu", "thai", 
                "vietnamese", "indonesian", "malay", "tamil", "telugu", "gujarati", 
                "marathi", "punjabi", "nepali", "sinhala", "myanmar", "khmer", "lao",
                "czech", "slovak", "hungarian", "romanian", "bulgarian", "croatian",
                "serbian", "slovenian", "lithuanian", "latvian", "estonian", "finnish",
                "danish", "swedish", "norwegian", "icelandic", "greek", "hebrew",
                "persian", "pashto", "dari", "kurdish", "azerbaijani", "kazakh",
                "kyrgyz", "uzbek", "tajik", "mongolian", "georgian", "armenian",
                "albanian", "macedonian", "bosnian", "maltese", "welsh", "irish",
                "scottish", "basque", "catalan", "galician", "afrikaans", "swahili",
                "hausa", "yoruba", "igbo", "amharic", "somali", "oromo", "tigrinya",
                "malagasy", "sesotho", "xhosa", "zulu"
            ]
        }
    elif "nllb" in model_name:
        return {
            "model": model_name,
            "supported_languages": [
                "english", "german", "french", "spanish", "italian", "portuguese",
                "russian", "arabic", "mandarin", "chinese", "japanese", "korean"
            ]
        }
    else:
        return {
            "model": model_name,
            "supported_languages": [
                "english", "german", "french", "spanish", "italian", "portuguese",
                "russian", "arabic", "chinese", "japanese", "korean"
            ]
        }

def _build_download_name(original: str, target_language: str) -> str:
    name, ext = os.path.splitext(original)
    return f"{name}.translated.{target_language}{ext}"

def _media_type_from_ext(ext: str) -> str:
    return {
        ".docx": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        ".pptx": "application/vnd.openxmlformats-officedocument.presentationml.presentation",
        ".pdf": "application/pdf",
        ".txt": "text/plain",
    }.get(ext, "application/octet-stream")
