import language_tool_python
from typing import Optional, List
import torch
from transformers import M2M100ForConditionalGeneration, M2M100Tokenizer, AutoModelForSeq2SeqLM, AutoTokenizer, pipeline

import threading
from concurrent.futures import ThreadPoolExecutor, as_completed


class TranslationService:
    """
    Advanced translation service: smart segmentation, context-aware batching, hardware acceleration, multi-model, streaming/hybrid.
    """
    _model_registry = {}
    _lock = threading.Lock()

    def __init__(self):
        self._model_name: Optional[str] = None
        self._tokenizer: Optional[M2M100Tokenizer] = None
        self._model: Optional[M2M100ForConditionalGeneration] = None
        self._pipe = None
        self._src_lang: Optional[str] = None
        self._tgt_lang: Optional[str] = None
        self._grammar_tool = language_tool_python.LanguageTool('en-US')

    def postprocess_text(self, text: str) -> str:
        # Grammar and sentence correction
        matches = self._grammar_tool.check(text)
        return language_tool_python.utils.correct(text, matches)

    def load_model_if_needed(self, model_name: str, device: str = "cuda") -> None:
        with self._lock:
            if model_name in self._model_registry:
                self._pipe, self._tokenizer, self._model = self._model_registry[model_name]
                self._model_name = model_name
                return
            self._model_name = model_name
            # Always use GPU if available
            use_device = 0 if torch.cuda.is_available() else -1
            if "m2m100" in model_name.lower():
                tokenizer = M2M100Tokenizer.from_pretrained(model_name)
                model = M2M100ForConditionalGeneration.from_pretrained(model_name)
                pipe = pipeline(
                    "translation",
                    model=model,
                    tokenizer=tokenizer,
                    device=use_device
                )
            else:
                tokenizer = AutoTokenizer.from_pretrained(model_name)
                model = AutoModelForSeq2SeqLM.from_pretrained(model_name)
                pipe = pipeline(
                    "translation",
                    model=model,
                    tokenizer=tokenizer,
                    device=use_device
                )
            self._pipe = pipe
            self._tokenizer = tokenizer
            self._model = model
            self._model_registry[model_name] = (pipe, tokenizer, model)

    def set_language_pair(self, src_iso: Optional[str], tgt_code: str) -> None:
        self._src_lang = src_iso
        self._tgt_lang = tgt_code

    def smart_segment(self, segments: List[str]) -> List[List[str]]:
        # Group segments by context (e.g., paragraphs in a section)
        # For now, group by 3 for context-aware batching
        grouped = []
        i = 0
        while i < len(segments):
            grouped.append(segments[i:i+3])
            i += 3
        return grouped

    def context_aware_batch_translate(self, segments: List[str], max_length: int = 2048, batch_size: int = 16) -> List[str]:
        if not segments:
            return []
        generate_kwargs = {}
        if self._model_name and "m2m100" in self._model_name.lower():
            if self._src_lang:
                self._tokenizer.src_lang = self._src_lang
            self._tokenizer.tgt_lang = self._tgt_lang
        elif self._model_name and "nllb" in self._model_name:
            if self._src_lang:
                self._tokenizer.src_lang = self._src_lang
                self._tokenizer.tgt_lang = self._tgt_lang
            generate_kwargs["src_lang"] = self._src_lang or "eng_Latn"
            generate_kwargs["tgt_lang"] = self._tgt_lang
        elif self._model_name and "mbart" in self._model_name:
            if hasattr(self._tokenizer, "lang_code_to_id"):
                generate_kwargs["forced_bos_token_id"] = self._tokenizer.lang_code_to_id.get(self._tgt_lang)
        results = []
        grouped = self.smart_segment(segments)
        for group in grouped:
            outputs = self._pipe(group, max_length=max_length, clean_up_tokenization_spaces=True, **generate_kwargs)
            for o in outputs:
                corrected = self.postprocess_text(o["translation_text"])
                results.append(corrected)
        return results

    def hybrid_translate(self, segments: List[str], max_length: int = 2048, batch_size: int = 16, parallel_workers: int = 4, models: List[str] = None) -> List[str]:
        # Use multiple models for different segments (e.g., titles, tables, body)
        if not segments:
            return []
        if not models:
            models = [self._model_name]
        results = [None] * len(segments)
        def translate_with_model(idx, seg, model_name):
            self.load_model_if_needed(model_name)
            return self.context_aware_batch_translate([seg], max_length=max_length, batch_size=batch_size)[0]
        with ThreadPoolExecutor(max_workers=parallel_workers) as executor:
            futures = []
            for idx, seg in enumerate(segments):
                model_name = models[idx % len(models)]
                futures.append(executor.submit(translate_with_model, idx, seg, model_name))
            for idx, future in enumerate(futures):
                try:
                    results[idx] = future.result()
                except Exception:
                    results[idx] = "ERROR"
        return results

    def stream_translate(self, segments: List[str], max_length: int = 2048, batch_size: int = 16):
        # Generator for streaming translation
        for i in range(0, len(segments), batch_size):
            batch = segments[i:i+batch_size]
            translated = self.context_aware_batch_translate(batch, max_length=max_length, batch_size=batch_size)
            for t in translated:
                yield t
