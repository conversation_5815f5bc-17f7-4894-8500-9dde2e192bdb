from typing import List
from pptx import Presentation
from pptx.slide import Slide
from pptx.shapes.base import BaseShape

"""
Note: Do not import Table from pptx.shapes.table; use shape.has_table and shape.table.
"""

from ..services.translator import TranslationService

def translate_pptx_file(input_path: str, output_path: str, svc: TranslationService) -> None:
    prs = Presentation(input_path)
    # Collect all paragraphs and table cell texts
    segments = []
    para_refs = []
    for slide in prs.slides:
        for shape in slide.shapes:
            if hasattr(shape, "has_text_frame") and shape.has_text_frame:
                for p in shape.text_frame.paragraphs:
                    segments.append(p.text)
                    para_refs.append((p, None))
            elif hasattr(shape, "has_table") and shape.has_table:
                tbl = shape.table
                for row in tbl.rows:
                    for cell in row.cells:
                        for p in cell.text_frame.paragraphs:
                            segments.append(p.text)
                            para_refs.append((p, cell))
    # Translate all segments in parallel batches
    translated = svc.context_aware_batch_translate(segments, batch_size=32)
    # Write back translations
    idx = 0
    for (p, cell) in para_refs:
        p.text = translated[idx]
        idx += 1
    prs.save(output_path)

def _translate_slide(slide: Slide, svc: TranslationService) -> None:
    for shape in slide.shapes:
        _translate_shape(shape, svc)

def _translate_shape(shape: BaseShape, svc: TranslationService) -> None:
    if hasattr(shape, "has_text_frame") and shape.has_text_frame:
        paras: List[str] = [p.text for p in shape.text_frame.paragraphs]
        translated = svc.batch_translate(paras)

        for p, t in zip(shape.text_frame.paragraphs, translated):
            p.text = t

    elif hasattr(shape, "has_table") and shape.has_table:
        tbl = shape.table

        for row in tbl.rows:
            for cell in row.cells:
                paras: List[str] = [p.text for p in cell.text_frame.paragraphs]
                translated = svc.batch_translate(paras)

                for p, t in zip(cell.text_frame.paragraphs, translated):
                    p.text = t

# NEW GRAMMAR CHECKING FUNCTIONS
def grammar_check_pptx_file(input_path: str, output_path: str, grammar_svc) -> None:
    """Grammar check PPTX file"""
    prs = Presentation(input_path)
    
    for slide in prs.slides:
        _grammar_check_slide(slide, grammar_svc)
    
    prs.save(output_path)

def _grammar_check_slide(slide: Slide, grammar_svc) -> None:
    for shape in slide.shapes:
        _grammar_check_shape(shape, grammar_svc)

def _grammar_check_shape(shape: BaseShape, grammar_svc) -> None:
    if hasattr(shape, "has_text_frame") and shape.has_text_frame:
        for p in shape.text_frame.paragraphs:
            if p.text.strip():
                p.text = grammar_svc.check_grammar(p.text)
    
    elif hasattr(shape, "has_table") and shape.has_table:
        tbl = shape.table
        for row in tbl.rows:
            for cell in row.cells:
                for p in cell.text_frame.paragraphs:
                    if p.text.strip():
                        p.text = grammar_svc.check_grammar(p.text)
