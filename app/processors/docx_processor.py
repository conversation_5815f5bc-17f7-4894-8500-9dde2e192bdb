from docx import Document
from docx.table import _Cell
from docx.text.paragraph import Paragraph
from typing import List
from ..services.translator import TranslationService

def translate_docx_file(input_path: str, output_path: str, svc: TranslationService) -> None:
    doc = Document(input_path)

    segments = [para.text for para in doc.paragraphs]
    translated = svc.context_aware_batch_translate(segments, batch_size=32)

    for para, new_text in zip(doc.paragraphs, translated):
        para.text = new_text

    doc.save(output_path)

def _translate_paragraph(paragraph: Paragraph, svc: TranslationService) -> None:
    if not paragraph.runs:
        if paragraph.text.strip():
            paragraph.text = svc.translate_text(paragraph.text)
        return

    # Preserve run formatting by translating per run, but keep punctuation joins natural
    texts: List[str] = [run.text for run in paragraph.runs]
    translated: List[str] = svc.batch_translate(texts)

    for run, new_text in zip(paragraph.runs, translated):
        run.text = new_text

def _translate_cell(cell: _Cell, svc: TranslationService) -> None:
    for p in cell.paragraphs:
        _translate_paragraph(p, svc)

    for tbl in cell.tables:
        for row in tbl.rows:
            for c in row.cells:
                _translate_cell(c, svc)

# NEW GRAMMAR CHECKING FUNCTIONS
def grammar_check_docx_file(input_path: str, output_path: str, grammar_svc) -> None:
    """Grammar check DOCX file"""
    doc = Document(input_path)
    
    # Grammar check paragraphs
    for paragraph in doc.paragraphs:
        _grammar_check_paragraph(paragraph, grammar_svc)
    
    # Grammar check tables
    for table in doc.tables:
        for row in table.rows:
            for cell in row.cells:
                _grammar_check_cell(cell, grammar_svc)
    
    doc.save(output_path)

def _grammar_check_paragraph(paragraph: Paragraph, grammar_svc) -> None:
    if not paragraph.text.strip():
        return
        
    corrected_text = grammar_svc.check_grammar(paragraph.text)
    
    # Replace text while preserving formatting
    if paragraph.runs:
        # Clear existing runs except first and add corrected text
        for run in paragraph.runs[1:]:
            run.clear()
        if paragraph.runs:
            paragraph.runs[0].text = corrected_text
    else:
        paragraph.text = corrected_text

def _grammar_check_cell(cell: _Cell, grammar_svc) -> None:
    for p in cell.paragraphs:
        _grammar_check_paragraph(p, grammar_svc)
    
    for tbl in cell.tables:
        for row in tbl.rows:
            for c in row.cells:
                _grammar_check_cell(c, grammar_svc)
