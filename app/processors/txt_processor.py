from ..services.translator import TranslationService

def translate_txt_file(input_path: str, output_path: str, svc) -> None:
    with open(input_path, "r", encoding="utf-8") as f:
        lines = f.readlines()

    translated = svc.context_aware_batch_translate(lines, batch_size=32)

    with open(output_path, "w", encoding="utf-8") as f:
        for line in translated:
            f.write(line + "\n")

# NEW GRAMMAR CHECKING FUNCTION
def grammar_check_txt_file(input_path: str, output_path: str, grammar_svc) -> None:
    """Grammar check TXT file"""
    with open(input_path, "r", encoding="utf-8", errors="ignore") as f:
        text = f.read()
    
    corrected_text = grammar_svc.check_grammar(text)
    
    with open(output_path, "w", encoding="utf-8") as f:
        f.write(corrected_text)
