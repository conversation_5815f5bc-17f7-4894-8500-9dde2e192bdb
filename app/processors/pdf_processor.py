import fitz  # PyMuPDF
from typing import List, Tuple
from ..services.translator import TranslationService

def translate_pdf_file(input_path: str, output_path: str, svc: TranslationService) -> None:
    # Strategy: extract text blocks with bbox, translate per block, and re-insert via text writers
    doc = fitz.open(input_path)
    new_doc = fitz.open()

    for page_index in range(len(doc)):
        page = doc[page_index]
        new_page = new_doc.new_page(width=page.rect.width, height=page.rect.height)

        # extract blocks keeping layout order
        blocks = page.get_text("blocks")  # list of (x0, y0, x1, y1, text, block_no, block_type)
        segments = [block[4] for block in blocks]
        translated = svc.context_aware_batch_translate(segments, batch_size=32)

        for (x0, y0, x1, y1, _txt, _bn, *_rest), new_text in zip(blocks, translated):
            rect = fitz.Rect(x0, y0, x1, y1)
            new_page.insert_textbox(rect, new_text, fontsize=12, fontname="helv", align=0)

    new_doc.save(output_path)
    new_doc.close()
    doc.close()

# NEW GRAMMAR CHECKING FUNCTION
def grammar_check_pdf_file(input_path: str, output_path: str, grammar_svc) -> None:
    """Grammar check PDF file"""
    doc = fitz.open(input_path)
    new_doc = fitz.open()
    
    for page_index in range(len(doc)):
        page = doc[page_index]
        new_page = new_doc.new_page(width=page.rect.width, height=page.rect.height)
        
        blocks = page.get_text("blocks")
        texts = [b[4] for b in blocks if b[4].strip()]
        
        # Grammar check each text block
        corrected_texts = grammar_svc.batch_grammar_check(texts)
        
        text_index = 0
        for (x0, y0, x1, y1, original_text, _bn, *_rest) in blocks:
            if original_text.strip():
                corrected_text = corrected_texts[text_index] if text_index < len(corrected_texts) else original_text
                text_index += 1
            else:
                corrected_text = original_text
            
            rect = fitz.Rect(x0, y0, x1, y1)
            new_page.insert_textbox(rect, corrected_text, fontsize=12, fontname="helv", align=0)
    
    new_doc.save(output_path)
    new_doc.close()
    doc.close()
