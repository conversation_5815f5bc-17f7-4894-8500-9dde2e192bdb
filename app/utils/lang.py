import os
from typing import Optional
from langdetect import detect

# Map user-friendly names to model codes

# NLLB language codes (existing)
NLLB_CODE_BY_NAME = {
    "english": "eng_Latn",
    "german": "deu_Latn",
    "french": "fra_Latn",
    "spanish": "spa_Latn",
    "italian": "ita_Latn",
    "portuguese": "por_Latn",
    "russian": "rus_Cyrl",
    "arabic": "arb_Arab",
    "mandarin": "zho_Hans",
    "chinese": "zho_<PERSON>",
    "japanese": "jpn_Jpan",
    "korean": "kor_Hang",
}

# mBART language codes (existing)
MBART_CODE_BY_NAME = {
    "english": "en_XX",
    "german": "de_DE",
    "french": "fr_XX",
    "spanish": "es_XX",
    "italian": "it_IT",
    "portuguese": "pt_XX",
    "russian": "ru_RU",
    "arabic": "ar_AR",
    "chinese": "zh_CN",
    "japanese": "ja_XX",
    "korean": "ko_KR",
}

# M2M100 language codes (NEW)
M2M100_CODE_BY_NAME = {
    "english": "en",
    "german": "de",
    "french": "fr",
    "spanish": "es",
    "italian": "it",
    "portuguese": "pt",
    "russian": "ru",
    "arabic": "ar",
    "chinese": "zh",
    "mandarin": "zh",
    "japanese": "ja",
    "korean": "ko",
    "dutch": "nl",
    "polish": "pl",
    "turkish": "tr",
    "hindi": "hi",
    "bengali": "bn",
    "urdu": "ur",
    "thai": "th",
    "vietnamese": "vi",
    "indonesian": "id",
    "malay": "ms",
    "tamil": "ta",
    "telugu": "te",
    "gujarati": "gu",
    "marathi": "mr",
    "punjabi": "pa",
    "nepali": "ne",
    "sinhala": "si",
    "myanmar": "my",
    "khmer": "km",
    "lao": "lo",
    "czech": "cs",
    "slovak": "sk",
    "hungarian": "hu",
    "romanian": "ro",
    "bulgarian": "bg",
    "croatian": "hr",
    "serbian": "sr",
    "slovenian": "sl",
    "lithuanian": "lt",
    "latvian": "lv",
    "estonian": "et",
    "finnish": "fi",
    "danish": "da",
    "swedish": "sv",
    "norwegian": "no",
    "icelandic": "is",
    "greek": "el",
    "hebrew": "he",
    "persian": "fa",
    "pashto": "ps",
    "dari": "prs",
    "kurdish": "ku",
    "azerbaijani": "az",
    "kazakh": "kk",
    "kyrgyz": "ky",
    "uzbek": "uz",
    "tajik": "tg",
    "mongolian": "mn",
    "georgian": "ka",
    "armenian": "hy",
    "albanian": "sq",
    "macedonian": "mk",
    "bosnian": "bs",
    "maltese": "mt",
    "welsh": "cy",
    "irish": "ga",
    "scottish": "gd",
    "basque": "eu",
    "catalan": "ca",
    "galician": "gl",
    "afrikaans": "af",
    "swahili": "sw",
    "hausa": "ha",
    "yoruba": "yo",
    "igbo": "ig",
    "amharic": "am",
    "somali": "so",
    "oromo": "om",
    "tigrinya": "ti",
    "malagasy": "mg",
    "sesotho": "st",
    "xhosa": "xh",
    "zulu": "zu",
}

def normalize_target(target_language: str, model_name: str) -> str:
    key = target_language.strip().lower()
    
    if "m2m100" in model_name.lower():
        return M2M100_CODE_BY_NAME.get(key, key)
    elif "nllb" in model_name:
        return NLLB_CODE_BY_NAME.get(key, key)
    else:
        return MBART_CODE_BY_NAME.get(key, key)

def detect_language_code(input_path: str, ext: str) -> Optional[str]:
    """Detect source language code for NLLB/MBART/M2M100 from the document text.
    Returns appropriate code based on model type.
    """
    text = _extract_text_for_detection(input_path, ext)
    if not text:
        return None

    try:
        lang = detect(text[:5000])
    except Exception:
        return None

    # Map ISO-639-1 to model codes (returns NLLB format by default)
    # You can modify this function to return M2M100 codes if needed
    if lang == "en":
        return "eng_Latn"  # NLLB format
    if lang == "de":
        return "deu_Latn"
    if lang == "fr":
        return "fra_Latn"
    if lang == "es":
        return "spa_Latn"
    if lang == "zh":
        return "zho_Hans"
    if lang == "ru":
        return "rus_Cyrl"
    if lang == "ar":
        return "arb_Arab"
    if lang == "ja":
        return "jpn_Jpan"
    if lang == "ko":
        return "kor_Hang"

    # fallback
    return None

def detect_language_code_for_model(input_path: str, ext: str, model_name: str) -> Optional[str]:
    """Detect source language code specific to the model being used."""
    text = _extract_text_for_detection(input_path, ext)
    if not text:
        return None

    try:
        lang = detect(text[:5000])
    except Exception:
        return None

    # Return appropriate format based on model
    if "m2m100" in model_name.lower():
        # M2M100 format
        m2m_mapping = {
            "en": "en", "de": "de", "fr": "fr", "es": "es", "zh": "zh",
            "ru": "ru", "ar": "ar", "ja": "ja", "ko": "ko", "it": "it",
            "pt": "pt", "nl": "nl", "pl": "pl", "tr": "tr", "hi": "hi"
        }
        return m2m_mapping.get(lang)
    
    elif "nllb" in model_name:
        # NLLB format
        nllb_mapping = {
            "en": "eng_Latn", "de": "deu_Latn", "fr": "fra_Latn", "es": "spa_Latn",
            "zh": "zho_Hans", "ru": "rus_Cyrl", "ar": "arb_Arab", "ja": "jpn_Jpan", "ko": "kor_Hang"
        }
        return nllb_mapping.get(lang)
    
    else:
        # mBART format
        mbart_mapping = {
            "en": "en_XX", "de": "de_DE", "fr": "fr_XX", "es": "es_XX",
            "zh": "zh_CN", "ru": "ru_RU", "ar": "ar_AR", "ja": "ja_XX", "ko": "ko_KR"
        }
        return mbart_mapping.get(lang)

def _extract_text_for_detection(input_path: str, ext: str) -> str:
    if ext == ".txt":
        try:
            with open(input_path, "r", encoding="utf-8", errors="ignore") as f:
                return f.read()
        except Exception:
            return ""

    if ext == ".docx":
        from docx import Document
        d = Document(input_path)
        return "\n".join(p.text for p in d.paragraphs)

    if ext == ".pptx":
        from pptx import Presentation
        prs = Presentation(input_path)
        parts = []
        for slide in prs.slides:
            for shape in slide.shapes:
                if hasattr(shape, "has_text_frame") and shape.has_text_frame:
                    parts.extend(p.text for p in shape.text_frame.paragraphs)
        return "\n".join(parts)

    if ext == ".pdf":
        import fitz
        doc = fitz.open(input_path)
        parts = []
        for page in doc:
            parts.append(page.get_text())
        doc.close()
        return "\n".join(parts)

    return ""
