import io
import requests
import streamlit as st


API_URL = "http://localhost:8000/translate-doc"


st.set_page_config(page_title="AI Document Translator", layout="centered")
st.title("AI Document Translator")
st.caption("Translate DOCX, PDF, PPTX, TXT using Hugging Face models while preserving formatting.")


with st.sidebar:
	st.header("Settings")
	model_name = st.selectbox(
		"Model",
		[
			"facebook/nllb-200-distilled-600M",
			"facebook/mbart-large-50-many-to-many-mmt",
			"facebook/m2m100_418M",
			"Helsinki-NLP/opus-mt-en-de",
		],
		index=0,
	)
	target_language = st.text_input("Target language (e.g., German, Mandarin, French)", value="German")


uploaded = st.file_uploader("Upload a document (.docx, .pdf, .pptx, .txt)", type=["docx", "pdf", "pptx", "txt"])

if st.button("Translate", disabled=uploaded is None):
	if uploaded is None:
		st.warning("Please upload a document.")
		st.stop()
	with st.spinner("Translating... This may take a while on first run."):
		files = {"file": (uploaded.name, uploaded.read(), uploaded.type or "application/octet-stream")}
		data = {"target_language": target_language, "model_name": model_name}
		try:
			resp = requests.post(API_URL, files=files, data=data, timeout=None)
		except Exception as e:
			st.error(f"Failed to call API: {e}")
			st.stop()
		if resp.status_code != 200:
			try:
				st.error(resp.json())
			except Exception:
				st.error(f"Server returned {resp.status_code}")
			st.stop()
		# Success - show a download button
		translated_bytes = resp.content
		download_name = resp.headers.get("content-disposition", f"translated-{uploaded.name}")
		st.success("Translation complete!")
		st.download_button(
			label="Download translated file",
			data=translated_bytes,
			file_name=uploaded.name.replace(".", ".translated."),
			mime=resp.headers.get("content-type", "application/octet-stream"),
		)


