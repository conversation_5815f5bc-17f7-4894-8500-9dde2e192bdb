# AI Document Translator

Translate full documents (DOCX, PDF, PPTX, TXT, CSV, JSON, XML, HTML, MD, RTF) using Hugging Face models while preserving structure, images, and tables.

## Features
- Preserves images/diagrams and layout (DOCX, PPTX, PDF overlay)
- Markdown-style tables: per-cell translation with divider preservation
- Multi-model support: NLLB, M2M100, mBART, OPUS
- Parallel processing and large-batch optimization for 20–30+ page docs
- Unified processor for all file types, robust structure preservation
- REST API (FastAPI) + optional Streamlit UI

## Supported File Types
- DOCX, PDF, PPTX, TXT
- CSV, JSON, XML, HTML/HTM, MD, RTF

## Quick Start

### 1) Install
```bash
# In project root
python -m venv venv
venv\Scripts\activate  # Windows
pip install -r requirements.txt

# Optional: GPU build of PyTorch (choose right CUDA if applicable)
# pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
```

### 2) Run API Server
```bash
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```
- Docs: http://localhost:8000/docs
- Health: http://localhost:8000/health

### 3) Translate a Document (curl)
```bash
curl -X POST "http://localhost:8000/translate-doc" \
  -F "file=@/path/to/input.pdf" \
  -F "target_language=de" \
  -F "model_name=Helsinki-NLP/opus-mt-en-de" \
  -F "enable_parallel=true" \
  -F "max_workers=4" \
  -F "batch_size=64" \
  -F "merge_short_segments=true" \
  -o translated.pdf
```

### 4) Optional: Streamlit UI
```bash
streamlit run streamlit_app.py
```
- UI: http://localhost:8501

## Usage (Python)
```python
from app.services.translator import TranslationService
from app.processors.unified_processor import ProcessingConfig, translate_pdf_file

svc = TranslationService(batch_size=128, max_workers=8)
svc.load_model_if_needed("facebook/nllb-200-distilled-600M")
svc.set_language_pair(src_iso="eng_Latn", tgt_code="deu_Latn")

config = ProcessingConfig(
	max_workers=8,
	batch_size=128,
	enable_parallel=True,
	merge_short_segments=True
)

translate_pdf_file("input.pdf", "output.pdf", svc, config)
```

## API Endpoints
- GET `/health` – service status
- GET `/models` – available models and current model info
- GET `/performance` – performance statistics (segments/sec, totals)
- POST `/switch-model` – change model (form: `model_name`, `src_lang`, `tgt_lang`)
- POST `/clear-stats` – reset performance stats
- POST `/translate-doc` – multipart form upload
  - `file`: the document
  - `target_language`: e.g., `de`, `fr`, `es`, or model-specific codes
  - `model_name`: e.g., `facebook/nllb-200-distilled-600M`, `Helsinki-NLP/opus-mt-en-de`, `facebook/m2m100_418M`, `facebook/mbart-large-50-many-to-many-mmt`
  - `enable_parallel` (bool), `max_workers` (int), `batch_size` (int), `merge_short_segments` (bool)

## Models
Pre-configured and auto-detected:
- `facebook/nllb-200-distilled-600M` (NLLB)
- `Helsinki-NLP/opus-mt-en-de` (OPUS)
- `facebook/m2m100_418M` (M2M100)
- `facebook/mbart-large-50-many-to-many-mmt` (mBART)

Notes:
- NLLB uses codes like `eng_Latn`, `deu_Latn`.
- mBART uses locale codes like `en_XX`, `de_DE`.

## Performance & Scaling Tips
- Use GPU if available (A100/V100/4090 recommended). On CPU, prefer smaller models.
- Increase `batch_size` to 64–128 for better throughput on GPU.
- Set `enable_parallel=true` and `max_workers=4–8` for 20–30+ page docs.
- Enable `merge_short_segments` to reduce overhead.
- PDFs: original page is copied; translated text is overlaid (images preserved).

## Markdown Tables
- Table rows and dividers auto-detected.
- Each cell translated individually with `cleanup` to avoid extra spacing.
- Divider rows (`|---|:--:|---|`) are preserved unmodified.

## Project Structure
```
app/
  main.py                     # FastAPI app and endpoints
  services/translator.py      # TranslationService (multi-model, parallel, batching)
  processors/unified_processor.py  # All file processors + optimizations
  utils/md_tables.py          # Markdown table helpers
streamlit_app.py              # Optional UI
requirements.txt
README.md
```

## Troubleshooting
- OOM/memory issues: lower `batch_size` (32/16) and `max_workers` (2–4).
- Slow performance: ensure GPU is used, increase batch size, enable parallel.
- Model download fails: check internet/disk space; try a smaller model.
- PDF text missing: scanned PDFs may require OCR before translation.

## Roadmap
- Async/background jobs with status polling
- Quantization/ONNX for faster CPU inference
- Distributed processing for 100+ page docs

## License
MIT

