import os
from typing import Optional
from langdetect import detect


# Map user-friendly names to model codes
NLLB_CODE_BY_NAME = {
	"english": "eng_Latn",
	"german": "deu_Latn",
	"french": "fra_Latn",
	"spanish": "spa_Latn",
	"italian": "ita_Latn",
	"portuguese": "por_Latn",
	"russian": "rus_Cyrl",
	"arabic": "arb_Arab",
	"mandarin": "zho_Hans",
	"chinese": "zho_Hans",
	"japanese": "jpn_Jpan",
	"korean": "kor_Hang",
}

MBART_CODE_BY_NAME = {
	"english": "en_XX",
	"german": "de_DE",
	"french": "fr_XX",
	"spanish": "es_XX",
	"italian": "it_IT",
	"portuguese": "pt_XX",
	"russian": "ru_RU",
	"arabic": "ar_AR",
	"chinese": "zh_CN",
	"japanese": "ja_XX",
	"korean": "ko_KR",
}


def normalize_target(target_language: str, model_name: str) -> str:
	key = target_language.strip().lower()
	if "nllb" in model_name:
		return NLLB_CODE_BY_NAME.get(key, key)
	return MBART_CODE_BY_NAME.get(key, key)


def detect_language_code(input_path: str, ext: str) -> Optional[str]:
	"""Detect source language code for NLLB/MBART from the document text.

	Returns ISO-like code for NLLB (e.g., "eng_Latn") when possible; otherwise None.
	"""
	text = _extract_text_for_detection(input_path, ext)
	if not text:
		return None
	try:
		lang = detect(text[:5000])
	except Exception:
		return None

	# Map ISO-639-1 to model codes (subset)
	if lang == "en":
		return "eng_Latn"
	if lang == "de":
		return "deu_Latn"
	if lang == "fr":
		return "fra_Latn"
	if lang == "es":
		return "spa_Latn"
	if lang == "zh":
		return "zho_Hans"
	if lang == "ru":
		return "rus_Cyrl"
	if lang == "ar":
		return "arb_Arab"
	if lang == "ja":
		return "jpn_Jpan"
	if lang == "ko":
		return "kor_Hang"
	# fallback
	return None


def _extract_text_for_detection(input_path: str, ext: str) -> str:
	if ext == ".txt":
		try:
			with open(input_path, "r", encoding="utf-8", errors="ignore") as f:
				return f.read()
		except Exception:
			return ""
	if ext == ".docx":
		from docx import Document
		d = Document(input_path)
		return "\n".join(p.text for p in d.paragraphs)
	if ext == ".pptx":
		from pptx import Presentation
		prs = Presentation(input_path)
		parts = []
		for slide in prs.slides:
			for shape in slide.shapes:
				if hasattr(shape, "has_text_frame") and shape.has_text_frame:
					parts.extend(p.text for p in shape.text_frame.paragraphs)
		return "\n".join(parts)
	if ext == ".pdf":
		import fitz
		doc = fitz.open(input_path)
		parts = []
		for page in doc:
			parts.append(page.get_text())
		doc.close()
		return "\n".join(parts)
	return ""


