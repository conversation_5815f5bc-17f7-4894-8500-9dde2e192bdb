"""
Markdown Table Utilities
Provides functions for detecting and processing markdown-style tables during translation.
"""

import re
from typing import List, Tuple, Optional


def is_table_row(line: str) -> bool:
    """
    Check if a line represents a markdown table row.
    Table rows start and end with | and contain | characters.
    """
    stripped = line.strip()
    return (stripped.startswith('|') and 
            stripped.endswith('|') and 
            '|' in stripped[1:-1])


def is_table_divider(line: str) -> bool:
	"""
	Check if a line represents a markdown table divider.
	Divider rows contain only |, -, :, and spaces.
	"""
	stripped = line.strip()
	if not (stripped.startswith('|') and stripped.endswith('|')):
		return False
	
	# Check if the content between | characters is only -, :, and spaces
	content = stripped[1:-1]
	# Split by | and check each cell
	cells = content.split('|')
	for cell in cells:
		cell = cell.strip()
		if cell and not re.match(r'^[\s\-\:]+$', cell):
			return False
	return True


def extract_table_cells(line: str) -> List[str]:
	"""
	Extract individual cells from a table row.
	Returns a list of cell contents (without the | characters).
	"""
	if not is_table_row(line):
		return [line]
	
	# Split by | and filter out empty parts
	parts = line.split('|')
	cells = [part.strip() for part in parts if part.strip()]
	
	return cells


def translate_table_row(line: str, translate_func, cleanup: bool = False) -> str:
	"""
	Translate a table row while preserving the | characters and structure.
	
	Args:
		line: The table row line
		translate_func: Function to translate text (should take text and return translated text)
		cleanup: If True, clean up extra spacing in translated text
	
	Returns:
		Translated table row with preserved formatting
	"""
	if not is_table_row(line):
		return translate_func(line)
	
	cells = extract_table_cells(line)
	translated_cells = []
	
	# Reconstruct the table row with translated content
	for i, cell in enumerate(cells):
		translated_text = translate_func(cell)
		if cleanup:
			translated_text = translated_text.strip()
		
		if i == 0:
			translated_cells.append('| ' + translated_text + ' |')
		else:
			translated_cells.append(' ' + translated_text + ' |')
	
	return ''.join(translated_cells)


def process_markdown_content(content: str, translate_func, cleanup: bool = False) -> str:
    """
    Process markdown content, detecting and translating table rows while preserving other content.
    
    Args:
        content: The markdown content as a string
        translate_func: Function to translate text
        cleanup: If True, clean up extra spacing in translated text
    
    Returns:
        Processed content with translated tables
    """
    lines = content.split('\n')
    processed_lines = []
    
    for line in lines:
        if is_table_divider(line):
            # Keep table dividers unchanged (check this first)
            processed_lines.append(line)
        elif is_table_row(line):
            # Translate table row while preserving formatting
            translated_line = translate_table_row(line, translate_func, cleanup)
            processed_lines.append(translated_line)
        else:
            # Process non-table lines normally
            if line.strip():
                translated_line = translate_func(line)
                processed_lines.append(translated_line)
            else:
                # Keep empty lines unchanged
                processed_lines.append(line)
    
    return '\n'.join(processed_lines)


def detect_table_blocks(content: str) -> List[Tuple[int, int]]:
    """
    Detect blocks of consecutive table lines in markdown content.
    
    Returns:
        List of tuples (start_line, end_line) for table blocks
    """
    lines = content.split('\n')
    table_blocks = []
    in_table = False
    start_line = 0
    
    for i, line in enumerate(lines):
        if is_table_row(line) or is_table_divider(line):
            if not in_table:
                in_table = True
                start_line = i
        elif in_table:
            # End of table block
            table_blocks.append((start_line, i - 1))
            in_table = False
    
    # Handle case where table ends at end of content
    if in_table:
        table_blocks.append((start_line, len(lines) - 1))
    
    return table_blocks
