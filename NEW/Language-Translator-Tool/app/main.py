from fastapi import <PERSON><PERSON><PERSON>, UploadFile, File, Form, HTTPException
from fastapi.responses import FileResponse, JSONResponse
from starlette.middleware.cors import CORSMiddleware
import os
import tempfile
import shutil
from uuid import uuid4
from typing import Optional

from .services.translator import TranslationService
from .processors.unified_processor import (
	translate_docx_file,
	translate_pptx_file,
	translate_pdf_file,
	translate_txt_file,
	get_supported_extensions,
	get_processor_for_file,
	get_media_type_for_extension
)
from .utils.lang import normalize_target, detect_language_code


app = FastAPI(title="AI Document Translator", version="1.0.0")

app.add_middleware(
	CORSMiddleware,
	allow_origins=["*"],
	allow_credentials=True,
	allow_methods=["*"],
	allow_headers=["*"],
)


translator_service = TranslationService()


@app.get("/health")
def health():
	return {"status": "ok"}


@app.get("/models")
def get_available_models():
	"""Get list of available translation models."""
	return {
		"available_models": translator_service.get_available_models(),
		"current_model": translator_service.get_current_model_info()
	}


@app.get("/performance")
def get_performance_stats():
	"""Get current performance statistics."""
	return translator_service.get_performance_stats()


@app.post("/switch-model")
async def switch_model(
	model_name: str = Form(...),
	src_lang: Optional[str] = Form(None),
	tgt_lang: str = Form(...)
):
	"""Switch to a different translation model."""
	try:
		translator_service.switch_model(model_name, src_lang, tgt_lang)
		return {"status": "success", "model": model_name}
	except Exception as e:
		raise HTTPException(status_code=500, detail=f"Failed to switch model: {str(e)}")


@app.post("/clear-stats")
def clear_performance_stats():
	"""Clear performance statistics."""
	translator_service.clear_stats()
	return {"status": "success", "message": "Performance statistics cleared"}


@app.post("/translate-doc")
async def translate_doc(
	file: UploadFile = File(...),
	target_language: str = Form(...),
	model_name: str = Form("facebook/nllb-200-distilled-600M"),
	enable_parallel: bool = Form(True),
	max_workers: int = Form(4),
	batch_size: int = Form(64),
	merge_short_segments: bool = Form(True)
):
	if not file.filename:
		raise HTTPException(status_code=400, detail="Missing filename")

	filename = file.filename
	ext = os.path.splitext(filename)[1].lower()
	if ext not in {".docx", ".pdf", ".pptx", ".txt"}:
		raise HTTPException(status_code=400, detail="Unsupported file type")

	# Prepare temp workspace
	tmp_dir = tempfile.mkdtemp(prefix="docxtr_")
	input_path = os.path.join(tmp_dir, f"input{ext}")
	output_path = os.path.join(tmp_dir, f"output{ext}")

	try:
		with open(input_path, "wb") as f:
			shutil.copyfileobj(file.file, f)

		# Configure translator with enhanced settings
		translator_service.load_model_if_needed(model_name)
		tgt_code = normalize_target(target_language, model_name)
		src_iso = detect_language_code(input_path, ext)
		# NLLB requires src_lang; fallback to English if detection/mapping failed
		if (src_iso is None) and ("nllb" in model_name):
			src_iso = "eng_Latn"
		translator_service.set_language_pair(src_iso=src_iso, tgt_code=tgt_code)
		
		# Create processing configuration for optimization
		from .processors.unified_processor import ProcessingConfig
		processing_config = ProcessingConfig(
			max_workers=max_workers,
			batch_size=batch_size,
			enable_parallel=enable_parallel,
			merge_short_segments=merge_short_segments
		)

		# Route by file type using unified processor
		processor = get_processor_for_file(filename)
		if processor:
			processor(input_path, output_path, translator_service, processing_config)
		else:
			raise HTTPException(status_code=400, detail="No processor found for file type")

		download_name = _build_download_name(filename, target_language)
		return FileResponse(
			output_path,
			media_type=_media_type_from_ext(ext),
			filename=download_name,
		)
	finally:
		# cleanup will be handled by FileResponse once streamed; we cannot remove immediately.
		pass


def _build_download_name(original: str, target_language: str) -> str:
	name, ext = os.path.splitext(original)
	return f"{name}.translated.{target_language}{ext}"


def _media_type_from_ext(ext: str) -> str:
	return get_media_type_for_extension(ext)


