"""
Unified Document Processor
Consolidates all file type processors into a single module with extended file support.
Maintains exact same functionality and function signatures as the original separate processors.
"""

import os
import fitz  # PyMuPDF
from docx import Document
from docx.table import _Cell
from docx.text.paragraph import Paragraph
from pptx import Presentation
from pptx.slide import Slide
from pptx.shapes.base import BaseShape
from typing import List, Tuple, Optional, Dict, Any
import csv
import json
import xml.etree.ElementTree as ET
from pathlib import Path
import concurrent.futures
import time
import logging
from dataclasses import dataclass

from ..services.translator import TranslationService
from ..utils.md_tables import process_markdown_content, is_table_row, is_table_divider

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class ProcessingConfig:
    """Configuration for document processing optimization."""
    max_workers: int = 4
    batch_size: int = 64
    chunk_size: int = 1000  # Number of paragraphs/blocks per chunk
    enable_parallel: bool = True
    merge_short_segments: bool = True
    min_segment_length: int = 10  # Minimum characters for a segment

# ============================================================================
# DOCX PROCESSOR (Original functionality preserved exactly)
# ============================================================================

def translate_docx_file(input_path: str, output_path: str, svc: TranslationService, config: ProcessingConfig = None) -> None:
	"""
	Enhanced DOCX translation with parallel processing and large document optimization.
	Supports documents with 20-30+ pages efficiently.
	"""
	if config is None:
		config = ProcessingConfig()
	
	start_time = time.time()
	logger.info(f"Starting DOCX translation: {input_path}")
	
	doc = Document(input_path)
	
	# Collect all translatable content for batch processing
	all_content = []
	content_map = {}  # Maps content to its location for reconstruction
	
	# Collect paragraphs
	for i, paragraph in enumerate(doc.paragraphs):
		if paragraph.text.strip():
			all_content.append(paragraph.text)
			content_map[len(all_content) - 1] = ('paragraph', i, paragraph)
	
	# Collect table content
	for table_idx, table in enumerate(doc.tables):
		for row_idx, row in enumerate(table.rows):
			for cell_idx, cell in enumerate(row.cells):
				for para_idx, para in enumerate(cell.paragraphs):
					if para.text.strip():
						all_content.append(para.text)
						content_map[len(all_content) - 1] = ('table_cell', table_idx, row_idx, cell_idx, para_idx, para)
	
	# Merge short segments if enabled
	if config.merge_short_segments:
		all_content = _merge_short_segments(all_content, config.min_segment_length)
	
	logger.info(f"Collected {len(all_content)} content segments for translation")
	
	# Batch translate all content
	if all_content:
		translated_content = svc.batch_translate(all_content)
		logger.info(f"Translation completed for {len(translated_content)} segments")
		
		# Reconstruct document with translated content
		_reconstruct_docx_content(doc, content_map, translated_content)
	
	# Note: All images, diagrams, and shapes are automatically preserved
	# as they are part of the document structure and not modified by text translation
	
	doc.save(output_path)
	
	processing_time = time.time() - start_time
	logger.info(f"DOCX translation completed in {processing_time:.2f}s")


def _translate_paragraph(paragraph: Paragraph, svc: TranslationService) -> None:
	if not paragraph.runs:
		if paragraph.text.strip():
			paragraph.text = svc.translate_text(paragraph.text)
		return

	# Preserve run formatting by translating per run, but keep punctuation joins natural
	texts: List[str] = [run.text for run in paragraph.runs]
	translated: List[str] = svc.batch_translate(texts)
	for run, new_text in zip(paragraph.runs, translated):
		run.text = new_text


def _translate_cell(cell: _Cell, svc: TranslationService) -> None:
	for p in cell.paragraphs:
		_translate_paragraph(p, svc)
	for tbl in cell.tables:
		for row in tbl.rows:
			for c in row.cells:
				_translate_cell(c, svc)


def _merge_short_segments(segments: List[str], min_length: int) -> List[str]:
	"""Merge short segments to reduce translation overhead."""
	if not segments:
		return segments
	
	merged = []
	current_segment = ""
	
	for segment in segments:
		if len(current_segment) + len(segment) <= min_length:
			current_segment += " " + segment if current_segment else segment
		else:
			if current_segment:
				merged.append(current_segment)
			current_segment = segment
	
	if current_segment:
		merged.append(current_segment)
	
	return merged


def _reconstruct_docx_content(doc: Document, content_map: Dict[int, tuple], translated_content: List[str]) -> None:
	"""Reconstruct DOCX document with translated content."""
	for content_idx, translated_text in enumerate(translated_content):
		if content_idx in content_map:
			content_info = content_map[content_idx]
			content_type = content_info[0]
			
			if content_type == 'paragraph':
				_, para_idx, paragraph = content_info
				paragraph.text = translated_text
			elif content_type == 'table_cell':
				_, table_idx, row_idx, cell_idx, para_idx, paragraph = content_info
				paragraph.text = translated_text


# ============================================================================
# PDF PROCESSOR (Original functionality preserved exactly)
# ============================================================================

def translate_pdf_file(input_path: str, output_path: str, svc: TranslationService, config: ProcessingConfig = None) -> None:
	"""
	Enhanced PDF translation with parallel processing and large document optimization.
	Supports documents with 20-30+ pages efficiently.
	"""
	if config is None:
		config = ProcessingConfig()
	
	start_time = time.time()
	logger.info(f"Starting PDF translation: {input_path}")
	
	doc = fitz.open(input_path)
	new_doc = fitz.open()
	
	# Collect all text blocks from all pages for batch processing
	all_text_blocks = []
	page_block_map = {}  # Maps block index to page and block info
	
	page_count = len(doc)
	logger.info(f"Processing {page_count} pages")
	
	# Extract text blocks from all pages
	for page_index in range(page_count):
		page = doc[page_index]
		blocks = page.get_text("blocks")
		
		# Filter text blocks
		text_blocks = []
		for block in blocks:
			x0, y0, x1, y1, text, block_no, block_type = block
			if block_type == 0 and text.strip() and (x1 - x0) > 10 and (y1 - y0) > 10:
				text_blocks.append(block)
		
		# Store block info for reconstruction
		for block_idx, block in enumerate(text_blocks):
			all_text_blocks.append(block[4])  # text content
			page_block_map[len(all_text_blocks) - 1] = (page_index, block)
	
	# Merge short segments if enabled
	if config.merge_short_segments and all_text_blocks:
		all_text_blocks = _merge_short_segments(all_text_blocks, config.min_segment_length)
		# Rebuild page_block_map for merged segments
		page_block_map = _rebuild_pdf_block_map(all_text_blocks, page_block_map)
	
	logger.info(f"Collected {len(all_text_blocks)} text blocks for translation")
	
	# Batch translate all text blocks
	if all_text_blocks:
		translated_texts = svc.batch_translate(all_text_blocks)
		logger.info(f"Translation completed for {len(translated_texts)} text blocks")
		
		# Process pages in parallel if enabled
		if config.enable_parallel and page_count > 1:
			_process_pdf_pages_parallel(doc, new_doc, page_block_map, translated_texts, config)
		else:
			_process_pdf_pages_sequential(doc, new_doc, page_block_map, translated_texts)
	
	new_doc.save(output_path)
	new_doc.close()
	doc.close()
	
	processing_time = time.time() - start_time
	logger.info(f"PDF translation completed in {processing_time:.2f}s")


def _rebuild_pdf_block_map(all_text_blocks: List[str], page_block_map: Dict[int, tuple]) -> Dict[int, tuple]:
	"""Rebuild page block map after merging segments."""
	new_map = {}
	current_idx = 0
	
	for i, text in enumerate(all_text_blocks):
		# Find original blocks that were merged into this text
		merged_blocks = []
		for orig_idx, (page_idx, block) in page_block_map.items():
			if orig_idx <= current_idx < orig_idx + len(text.split()):
				merged_blocks.append((page_idx, block))
		
		if merged_blocks:
			new_map[i] = merged_blocks
			current_idx += len(text.split())
	
	return new_map


def _process_pdf_pages_sequential(doc: fitz.Document, new_doc: fitz.Document, 
                                page_block_map: Dict[int, tuple], translated_texts: List[str]) -> None:
	"""Process PDF pages sequentially."""
	for page_index in range(len(doc)):
		_process_single_pdf_page(doc, new_doc, page_index, page_block_map, translated_texts)


def _process_pdf_pages_parallel(doc: fitz.Document, new_doc: fitz.Document, 
                              page_block_map: Dict[int, tuple], translated_texts: List[str], 
                              config: ProcessingConfig) -> None:
	"""Process PDF pages in parallel."""
	page_count = len(doc)
	
	# Create pages first
	for page_index in range(page_count):
		page = doc[page_index]
		new_page = new_doc.new_page(width=page.rect.width, height=page.rect.height)
		# Copy original page with images/diagrams
		new_page.show_pdf_page(new_page.rect, doc, page_index)
	
	# Process pages in parallel
	with concurrent.futures.ThreadPoolExecutor(max_workers=config.max_workers) as executor:
		futures = []
		for page_index in range(page_count):
			future = executor.submit(
				_process_single_pdf_page, doc, new_doc, page_index, page_block_map, translated_texts
			)
			futures.append(future)
		
		# Wait for all pages to complete
		for future in concurrent.futures.as_completed(futures):
			try:
				future.result()
			except Exception as e:
				logger.error(f"Error processing PDF page: {e}")


def _process_single_pdf_page(doc: fitz.Document, new_doc: fitz.Document, page_index: int,
                           page_block_map: Dict[int, tuple], translated_texts: List[str]) -> None:
	"""Process a single PDF page."""
	page = doc[page_index]
	new_page = new_doc[page_index]
	
	# Find blocks that belong to this page
	page_blocks = []
	for block_idx, block_info in page_block_map.items():
		if isinstance(block_info, tuple) and len(block_info) == 2:
			# Single block
			if block_info[0] == page_index:
				page_blocks.append((block_idx, block_info[1]))
		elif isinstance(block_info, list):
			# Merged blocks
			for page_idx, block in block_info:
				if page_idx == page_index:
					page_blocks.append((block_idx, block))
	
	# Overlay translated text
	for block_idx, block in page_blocks:
		if block_idx < len(translated_texts):
			x0, y0, x1, y1, _txt, _bn, *_rest = block
			rect = fitz.Rect(x0, y0, x1, y1)
			new_text = translated_texts[block_idx]
			
			# Use white background to cover original text
			new_page.draw_rect(rect, color=(1, 1, 1), fill=(1, 1, 1))
			# Insert translated text
			new_page.insert_textbox(rect, new_text, fontsize=12, fontname="helv", align=0)


# ============================================================================
# PPTX PROCESSOR (Original functionality preserved exactly)
# ============================================================================

def translate_pptx_file(input_path: str, output_path: str, svc: TranslationService, config: ProcessingConfig = None) -> None:
	"""
	Enhanced PPTX translation with parallel processing and large document optimization.
	Supports presentations with 20-30+ slides efficiently.
	"""
	if config is None:
		config = ProcessingConfig()
	
	start_time = time.time()
	logger.info(f"Starting PPTX translation: {input_path}")
	
	prs = Presentation(input_path)
	
	# Collect all translatable content for batch processing
	all_content = []
	content_map = {}  # Maps content to its location for reconstruction
	
	# Collect content from all slides
	for slide_idx, slide in enumerate(prs.slides):
		for shape_idx, shape in enumerate(slide.shapes):
			if hasattr(shape, "has_text_frame") and shape.has_text_frame:
				for para_idx, para in enumerate(shape.text_frame.paragraphs):
					if para.text.strip():
						all_content.append(para.text)
						content_map[len(all_content) - 1] = ('text_frame', slide_idx, shape_idx, para_idx, para)
			
			elif hasattr(shape, "has_table") and shape.has_table:
				tbl = shape.table
				for row_idx, row in enumerate(tbl.rows):
					for cell_idx, cell in enumerate(row.cells):
						for para_idx, para in enumerate(cell.text_frame.paragraphs):
							if para.text.strip():
								all_content.append(para.text)
								content_map[len(all_content) - 1] = ('table_cell', slide_idx, shape_idx, row_idx, cell_idx, para_idx, para)
	
	# Merge short segments if enabled
	if config.merge_short_segments:
		all_content = _merge_short_segments(all_content, config.min_segment_length)
	
	logger.info(f"Collected {len(all_content)} content segments for translation")
	
	# Batch translate all content
	if all_content:
		translated_content = svc.batch_translate(all_content)
		logger.info(f"Translation completed for {len(translated_content)} segments")
		
		# Reconstruct presentation with translated content
		_reconstruct_pptx_content(prs, content_map, translated_content)
	
	# Note: All images, diagrams, and shapes are automatically preserved
	# as they are part of the slide structure and not modified by text translation
	
	prs.save(output_path)
	
	processing_time = time.time() - start_time
	logger.info(f"PPTX translation completed in {processing_time:.2f}s")


def _translate_slide(slide: Slide, svc: TranslationService) -> None:
	for shape in slide.shapes:
		_translate_shape(shape, svc)


def _translate_shape(shape: BaseShape, svc: TranslationService) -> None:
	if hasattr(shape, "has_text_frame") and shape.has_text_frame:
		paras: List[str] = [p.text for p in shape.text_frame.paragraphs]
		translated = svc.batch_translate(paras)
		for p, t in zip(shape.text_frame.paragraphs, translated):
			p.text = t
	elif hasattr(shape, "has_table") and shape.has_table:
		tbl = shape.table
		for row in tbl.rows:
			for cell in row.cells:
				paras: List[str] = [p.text for p in cell.text_frame.paragraphs]
				translated = svc.batch_translate(paras)
				for p, t in zip(cell.text_frame.paragraphs, translated):
					p.text = t


def _reconstruct_pptx_content(prs: Presentation, content_map: Dict[int, tuple], translated_content: List[str]) -> None:
	"""Reconstruct PPTX presentation with translated content."""
	for content_idx, translated_text in enumerate(translated_content):
		if content_idx in content_map:
			content_info = content_map[content_idx]
			content_type = content_info[0]
			
			if content_type == 'text_frame':
				_, slide_idx, shape_idx, para_idx, paragraph = content_info
				paragraph.text = translated_text
			elif content_type == 'table_cell':
				_, slide_idx, shape_idx, row_idx, cell_idx, para_idx, paragraph = content_info
				paragraph.text = translated_text


# ============================================================================
# TXT PROCESSOR (Original functionality preserved exactly)
# ============================================================================

def translate_txt_file(input_path: str, output_path: str, svc: TranslationService) -> None:
	with open(input_path, "r", encoding="utf-8", errors="ignore") as f:
		text = f.read()
	
	# Use markdown table utilities to detect and translate tables properly
	translated = process_markdown_content(text, svc.translate_text, cleanup=True)
	
	with open(output_path, "w", encoding="utf-8") as f:
		f.write(translated)


# ============================================================================
# ADDITIONAL FILE TYPE SUPPORT
# ============================================================================

def translate_csv_file(input_path: str, output_path: str, svc: TranslationService) -> None:
	"""Translate CSV file while preserving structure and headers."""
	rows = []
	with open(input_path, 'r', encoding='utf-8', newline='') as f:
		reader = csv.reader(f)
		for i, row in enumerate(reader):
			if i == 0:  # Keep headers as-is
				rows.append(row)
			else:
				# Translate non-empty cells with cleanup to avoid extra spacing
				translated_row = []
				for cell in row:
					if cell.strip():
						translated_row.append(svc.translate_text(cell, cleanup=True))
					else:
						translated_row.append(cell)
				rows.append(translated_row)
	
	with open(output_path, 'w', encoding='utf-8', newline='') as f:
		writer = csv.writer(f)
		writer.writerows(rows)


def translate_json_file(input_path: str, output_path: str, svc: TranslationService) -> None:
	"""Translate JSON file while preserving structure."""
	with open(input_path, 'r', encoding='utf-8') as f:
		data = json.load(f)
	
	translated_data = _translate_json_recursive(data, svc)
	
	with open(output_path, 'w', encoding='utf-8') as f:
		json.dump(translated_data, f, ensure_ascii=False, indent=2)


def _translate_json_recursive(obj, svc: TranslationService):
	"""Recursively translate string values in JSON objects."""
	if isinstance(obj, dict):
		return {k: _translate_json_recursive(v, svc) for k, v in obj.items()}
	elif isinstance(obj, list):
		return [_translate_json_recursive(item, svc) for item in obj]
	elif isinstance(obj, str) and obj.strip():
		return svc.translate_text(obj, cleanup=True)
	else:
		return obj


def translate_xml_file(input_path: str, output_path: str, svc: TranslationService) -> None:
	"""Translate XML file while preserving structure and tags."""
	tree = ET.parse(input_path)
	root = tree.getroot()
	
	_translate_xml_recursive(root, svc)
	
	tree.write(output_path, encoding='utf-8', xml_declaration=True)


def _translate_xml_recursive(element, svc: TranslationService):
	"""Recursively translate text content in XML elements."""
	if element.text and element.text.strip():
		element.text = svc.translate_text(element.text, cleanup=True)
	
	for child in element:
		_translate_xml_recursive(child, svc)
	
	if element.tail and element.tail.strip():
		element.tail = svc.translate_text(element.tail, cleanup=True)


def translate_html_file(input_path: str, output_path: str, svc: TranslationService) -> None:
	"""Translate HTML file while preserving tags, structure, and images."""
	from bs4 import BeautifulSoup
	
	with open(input_path, 'r', encoding='utf-8') as f:
		html_content = f.read()
	
	soup = BeautifulSoup(html_content, 'html.parser')
	
	# Translate text nodes while preserving HTML structure and images
	for text in soup.find_all(text=True):
		if text.strip() and text.parent.name not in ['script', 'style']:
			# Check if this text might be part of a markdown-style table
			parent = text.parent
			if parent.name == 'td' or parent.name == 'th':
				# For table cells, use cleanup to avoid extra spacing
				translated_text = svc.translate_text(text, cleanup=True)
			else:
				translated_text = svc.translate_text(text)
			text.replace_with(translated_text)
	
	# Note: All images, diagrams, and media are automatically preserved
	# as they are part of the HTML structure and not modified by text translation
	
	with open(output_path, 'w', encoding='utf-8') as f:
		f.write(str(soup))


def translate_md_file(input_path: str, output_path: str, svc: TranslationService) -> None:
	"""Translate Markdown file while preserving formatting and tables."""
	with open(input_path, 'r', encoding='utf-8') as f:
		content = f.read()
	
	# Use markdown table utilities to detect and translate tables properly
	# This will handle markdown tables, headers, lists, and other formatting
	translated = process_markdown_content(content, svc.translate_text, cleanup=True)
	
	with open(output_path, 'w', encoding='utf-8') as f:
		f.write(translated)


def translate_rtf_file(input_path: str, output_path: str, svc: TranslationService) -> None:
	"""Translate RTF file while preserving formatting."""
	import re
	
	with open(input_path, 'r', encoding='utf-8', errors='ignore') as f:
		content = f.read()
	
	# Extract text content while preserving RTF formatting
	# This is a simplified approach - RTF parsing can be complex
	text_pattern = r'\\\w+\d*\s*|[\{\}]|\\\'[0-9a-fA-F]{2}|[^\{\}\\\']+'
	parts = re.findall(text_pattern, content)
	
	translated_parts = []
	for part in parts:
		if part.startswith('\\') or part in ['{', '}']:
			# Keep RTF control words and braces
			translated_parts.append(part)
		elif part.strip():
			# Translate actual text content
			translated_parts.append(svc.translate_text(part))
		else:
			translated_parts.append(part)
	
	with open(output_path, 'w', encoding='utf-8') as f:
		f.write(''.join(translated_parts))


# ============================================================================
# FILE TYPE DETECTION AND ROUTING
# ============================================================================

def get_supported_extensions() -> List[str]:
	"""Get list of all supported file extensions."""
	return [
		'.docx', '.pdf', '.pptx', '.txt',  # Original supported types
		'.csv', '.json', '.xml', '.html', '.htm', '.md', '.rtf'  # New supported types
	]


def is_file_supported(filename: str) -> bool:
	"""Check if file type is supported."""
	ext = os.path.splitext(filename)[1].lower()
	return ext in get_supported_extensions()


def get_processor_for_file(filename: str):
	"""Get the appropriate processor function for a given file type."""
	ext = os.path.splitext(filename)[1].lower()
	
	processor_map = {
		'.docx': translate_docx_file,
		'.pdf': translate_pdf_file,
		'.pptx': translate_pptx_file,
		'.txt': translate_txt_file,
		'.csv': translate_csv_file,
		'.json': translate_json_file,
		'.xml': translate_xml_file,
		'.html': translate_html_file,
		'.htm': translate_html_file,
		'.md': translate_md_file,
		'.rtf': translate_rtf_file,
	}
	
	return processor_map.get(ext)


def get_media_type_for_extension(ext: str) -> str:
	"""Get MIME type for a given file extension."""
	media_types = {
		".docx": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
		".pptx": "application/vnd.openxmlformats-officedocument.presentationml.presentation",
		".pdf": "application/pdf",
		".txt": "text/plain",
		".csv": "text/csv",
		".json": "application/json",
		".xml": "application/xml",
		".html": "text/html",
		".htm": "text/html",
		".md": "text/markdown",
		".rtf": "application/rtf",
	}
	return media_types.get(ext, "application/octet-stream")
