from typing import Optional, List, Dict, Any, Tuple
import torch
from transformers import AutoModelForSeq2SeqLM, AutoTokenizer, pipeline
import concurrent.futures
import asyncio
from functools import lru_cache
import time
import logging
import threading
from dataclasses import dataclass
from enum import Enum


class ModelType(Enum):
    NLLB = "nllb"
    MBART = "mbart"
    OPUS = "opus"
    M2M100 = "m2m100"
    GENERIC = "generic"


@dataclass
class ModelConfig:
    name: str
    type: ModelType
    src_lang: Optional[str]
    tgt_lang: str
    max_length: int = 1024
    device: str = "auto"
    dtype: Optional[str] = None


class TranslationService:
    """
    Enhanced Translation Service with multi-model support, parallel processing,
    and optimization for large documents (20-30+ pages).
    """
    
    def __init__(self, batch_size: int = 64, max_workers: int = 4, enable_quantization: bool = False):
        self._models: Dict[str, Dict[str, Any]] = {}
        self._current_model: Optional[str] = None
        self._batch_size: int = batch_size
        self._max_workers: int = max_workers
        self._enable_quantization = enable_quantization
        
        # Thread pool for parallel processing
        self._executor = concurrent.futures.ThreadPoolExecutor(max_workers=max_workers)
        self._lock = threading.Lock()
        
        # Configure logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        # Performance metrics and monitoring
        self._translation_stats = {
            'total_segments': 0,
            'total_tokens': 0,
            'total_time': 0.0,
            'batch_count': 0,
            'model_load_time': 0.0,
            'parallel_batches': 0
        }
        
        # Predefined model configurations
        self._model_configs = {
            "facebook/nllb-200-distilled-600M": ModelConfig(
                "facebook/nllb-200-distilled-600M", ModelType.NLLB, None, "eng_Latn"
            ),
            "Helsinki-NLP/opus-mt-en-de": ModelConfig(
                "Helsinki-NLP/opus-mt-en-de", ModelType.OPUS, "en", "de"
            ),
            "facebook/m2m100_418M": ModelConfig(
                "facebook/m2m100_418M", ModelType.M2M100, "en", "de"
            ),
            "facebook/mbart-large-50-many-to-many-mmt": ModelConfig(
                "facebook/mbart-large-50-many-to-many-mmt", ModelType.MBART, "en_XX", "de_DE"
            )
        }
        
        self.logger.info(f"TranslationService initialized with batch_size={batch_size}, max_workers={max_workers}")

    def get_available_models(self) -> List[str]:
        """Get list of available pre-configured models."""
        return list(self._model_configs.keys())

    def load_model_if_needed(self, model_name: str, src_lang: Optional[str] = None, tgt_lang: Optional[str] = None) -> None:
        """Load and cache model with optimization for large documents."""
        start_time = time.time()
        
        with self._lock:
            if model_name in self._models and self._models[model_name]['loaded']:
                self._current_model = model_name
                self.logger.info(f"Model {model_name} already loaded, using cached version")
                return
            
            self.logger.info(f"Loading model: {model_name}")
            
            # Get or create model configuration
            if model_name in self._model_configs:
                config = self._model_configs[model_name]
                if src_lang:
                    config.src_lang = src_lang
                if tgt_lang:
                    config.tgt_lang = tgt_lang
            else:
                # Auto-detect model type
                model_type = self._detect_model_type(model_name)
                config = ModelConfig(
                    name=model_name,
                    type=model_type,
                    src_lang=src_lang,
                    tgt_lang=tgt_lang or "en"
                )
            
            # Determine device and dtype
            device = self._get_optimal_device()
            dtype = self._get_optimal_dtype()
            
            try:
                # Load tokenizer and model with optimizations
                tokenizer = AutoTokenizer.from_pretrained(model_name)
                model = AutoModelForSeq2SeqLM.from_pretrained(
                    model_name,
                    torch_dtype=dtype,
                    device_map=device if device != "auto" else None,
                    low_cpu_mem_usage=True
                )
                
                # Move to device if not using device_map
                if device != "auto" and hasattr(model, 'to'):
                    model = model.to(device)
                
                # Create pipeline with optimizations
                pipe = pipeline(
                    "translation",
                    model=model,
                    tokenizer=tokenizer,
                    batch_size=self._batch_size,
                    torch_dtype=dtype
                )
                
                # Cache the loaded model
                self._models[model_name] = {
                    'config': config,
                    'tokenizer': tokenizer,
                    'model': model,
                    'pipe': pipe,
                    'loaded': True,
                    'device': device,
                    'dtype': dtype
                }
                
                self._current_model = model_name
                load_time = time.time() - start_time
                self._translation_stats['model_load_time'] += load_time
                
                self.logger.info(f"Model {model_name} loaded successfully in {load_time:.2f}s on {device}")
                
            except Exception as e:
                self.logger.error(f"Failed to load model {model_name}: {e}")
                raise

    def _detect_model_type(self, model_name: str) -> ModelType:
        """Auto-detect model type from name."""
        model_name_lower = model_name.lower()
        if "nllb" in model_name_lower:
            return ModelType.NLLB
        elif "mbart" in model_name_lower:
            return ModelType.MBART
        elif "opus" in model_name_lower:
            return ModelType.OPUS
        elif "m2m100" in model_name_lower:
            return ModelType.M2M100
        else:
            return ModelType.GENERIC

    def _get_optimal_device(self) -> str:
        """Get optimal device for translation."""
        if torch.cuda.is_available():
            # Check GPU memory and select best GPU
            gpu_count = torch.cuda.device_count()
            if gpu_count > 0:
                # Select GPU with most free memory
                best_gpu = 0
                max_memory = 0
                for i in range(gpu_count):
                    memory = torch.cuda.get_device_properties(i).total_memory
                    if memory > max_memory:
                        max_memory = memory
                        best_gpu = i
                return f"cuda:{best_gpu}"
        return "cpu"

    def _get_optimal_dtype(self) -> Optional[str]:
        """Get optimal data type for translation."""
        if torch.cuda.is_available():
            return "float16"  # Use half precision for GPU
        return None

    def set_language_pair(self, src_iso: Optional[str], tgt_code: str) -> None:
        """Set source and target language codes."""
        if self._current_model and self._current_model in self._models:
            config = self._models[self._current_model]['config']
            config.src_lang = src_iso
            config.tgt_lang = tgt_code
            self.logger.info(f"Language pair set: {src_iso} -> {tgt_code}")

    def translate_text(self, text: str, max_length: int = 1024, cleanup: bool = False) -> str:
        """Translate single text with optimization."""
        if not text or not text.strip():
            return text
        
        start_time = time.time()
        
        # Use batch translation for single text (more efficient)
        result = self.batch_translate([text], max_length)
        translated_text = result[0] if result else text
        
        # Apply cleanup if requested
        if cleanup:
            translated_text = translated_text.strip()
        
        # Update stats
        self._update_stats(1, len(text.split()), time.time() - start_time)
        
        return translated_text

    def batch_translate(self, segments: List[str], max_length: int = 1024) -> List[str]:
        """Enhanced batch translation with parallel processing for large batches."""
        if not segments:
            return []
        
        start_time = time.time()
        
        # Filter out empty segments
        valid_segments = [(i, seg) for i, seg in enumerate(segments) if seg and seg.strip()]
        if not valid_segments:
            return [""] * len(segments)
        
        # Split into optimal batch sizes
        batches = self._create_optimal_batches(valid_segments)
        
        # Process batches in parallel if multiple batches
        if len(batches) > 1 and self._max_workers > 1:
            results = self._parallel_batch_translate(batches, max_length)
        else:
            results = self._sequential_batch_translate(batches, max_length)
        
        # Reconstruct results in original order
        final_results = [""] * len(segments)
        for batch_result in results:
            for idx, translated_text in batch_result:
                final_results[idx] = translated_text
        
        # Update stats
        total_tokens = sum(len(seg.split()) for seg in segments if seg)
        self._update_stats(len(segments), total_tokens, time.time() - start_time)
        
        return final_results

    def _create_optimal_batches(self, segments: List[Tuple[int, str]]) -> List[List[Tuple[int, str]]]:
        """Create optimal batches for translation."""
        batches = []
        current_batch = []
        current_tokens = 0
        max_tokens_per_batch = 8000  # Optimal token count per batch
        
        for idx, segment in segments:
            segment_tokens = len(segment.split())
            
            # Start new batch if current would exceed token limit
            if current_tokens + segment_tokens > max_tokens_per_batch and current_batch:
                batches.append(current_batch)
                current_batch = []
                current_tokens = 0
            
            current_batch.append((idx, segment))
            current_tokens += segment_tokens
        
        # Add final batch
        if current_batch:
            batches.append(current_batch)
        
        return batches

    def _sequential_batch_translate(self, batches: List[List[Tuple[int, str]]], max_length: int) -> List[List[Tuple[int, str]]]:
        """Translate batches sequentially."""
        results = []
        for batch in batches:
            batch_result = self._translate_batch(batch, max_length)
            results.append(batch_result)
        return results

    def _parallel_batch_translate(self, batches: List[List[Tuple[int, str]]], max_length: int) -> List[List[Tuple[int, str]]]:
        """Translate batches in parallel using thread pool."""
        self.logger.info(f"Processing {len(batches)} batches in parallel with {self._max_workers} workers")
        
        futures = []
        for batch in batches:
            future = self._executor.submit(self._translate_batch, batch, max_length)
            futures.append(future)
        
        results = []
        for future in concurrent.futures.as_completed(futures):
            try:
                result = future.result()
                results.append(result)
            except Exception as e:
                self.logger.error(f"Batch translation failed: {e}")
                # Return empty results for failed batch
                results.append([])
        
        self._translation_stats['parallel_batches'] += len(batches)
        return results

    def _translate_batch(self, batch: List[Tuple[int, str]], max_length: int) -> List[Tuple[int, str]]:
        """Translate a single batch of segments."""
        if not self._current_model or self._current_model not in self._models:
            raise RuntimeError("No model loaded")
        
        model_info = self._models[self._current_model]
        pipe = model_info['pipe']
        config = model_info['config']
        
        # Extract text segments
        segments = [seg for _, seg in batch]
        
        # Prepare generation kwargs based on model type
        generate_kwargs = self._prepare_generation_kwargs(config, max_length)
        
        try:
            # Perform translation
            outputs = pipe(
                segments,
                max_length=max_length,
                clean_up_tokenization_spaces=True,
                batch_size=min(len(segments), self._batch_size),
                **generate_kwargs
            )
            
            # Extract results
            results = []
            for i, (idx, _) in enumerate(batch):
                if i < len(outputs):
                    translated_text = outputs[i]["translation_text"]
                    results.append((idx, translated_text))
                else:
                    results.append((idx, ""))
            
            return results
            
        except Exception as e:
            self.logger.error(f"Translation failed for batch: {e}")
            # Return empty results for failed batch
            return [(idx, "") for idx, _ in batch]

    def _prepare_generation_kwargs(self, config: ModelConfig, max_length: int) -> Dict[str, Any]:
        """Prepare generation kwargs based on model type."""
        kwargs = {}
        
        if config.type == ModelType.NLLB:
            if config.src_lang:
                kwargs["src_lang"] = config.src_lang
            kwargs["tgt_lang"] = config.src_lang or "eng_Latn"
        elif config.type == ModelType.MBART:
            if hasattr(self._models[self._current_model]['tokenizer'], "lang_code_to_id"):
                kwargs["forced_bos_token_id"] = self._models[self._current_model]['tokenizer'].lang_code_to_id.get(config.tgt_lang)
        elif config.type == ModelType.OPUS:
            # OPUS models typically don't need special kwargs
            pass
        elif config.type == ModelType.M2M100:
            # M2M100 models handle language codes automatically
            pass
        
        return kwargs

    def _update_stats(self, segments: int, tokens: int, time_taken: float):
        """Update translation statistics."""
        self._translation_stats['total_segments'] += segments
        self._translation_stats['total_tokens'] += tokens
        self._translation_stats['total_time'] += time_taken
        self._translation_stats['batch_count'] += 1

    def get_performance_stats(self) -> Dict[str, Any]:
        """Get current performance statistics."""
        stats = self._translation_stats.copy()
        if stats['total_segments'] > 0:
            stats['avg_time_per_segment'] = stats['total_time'] / stats['total_segments']
            stats['avg_time_per_token'] = stats['total_time'] / stats['total_tokens'] if stats['total_tokens'] > 0 else 0
            stats['segments_per_second'] = stats['total_segments'] / stats['total_time'] if stats['total_time'] > 0 else 0
        return stats

    def clear_stats(self):
        """Clear performance statistics."""
        self._translation_stats = {
            'total_segments': 0,
            'total_tokens': 0,
            'total_time': 0.0,
            'batch_count': 0,
            'model_load_time': 0.0,
            'parallel_batches': 0
        }

    def switch_model(self, model_name: str, src_lang: Optional[str] = None, tgt_lang: Optional[str] = None) -> None:
        """Switch to a different model."""
        self.load_model_if_needed(model_name, src_lang, tgt_lang)

    def get_current_model_info(self) -> Optional[Dict[str, Any]]:
        """Get information about the currently loaded model."""
        if self._current_model and self._current_model in self._models:
            model_info = self._models[self._current_model].copy()
            model_info['name'] = self._current_model
            return model_info
        return None

    def __del__(self):
        """Cleanup resources."""
        if hasattr(self, '_executor'):
            self._executor.shutdown(wait=True)


